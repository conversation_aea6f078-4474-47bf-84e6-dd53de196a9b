<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated CSS Demo</title>
    <link rel="stylesheet" href="Animated.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
        }
        
        .container {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        h1 {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            margin: 0;
        }
        
        /* Adding the missing keyframe animation */
        @keyframes in {
            0% {
                transform: translate(-300px, -50%);
                opacity: 0;
            }
            100% {
                transform: translate(-50%, -50%);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Animated CSS Demo</h1>
        <div class="cake"></div>
    </div>
</body>
</html>
