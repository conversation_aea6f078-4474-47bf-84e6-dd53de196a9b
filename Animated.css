.cake{
    background-color: #fff;
    border-radius: 10px;
    position:absolute;
    width:5px;
    height:5px;
    top:50%;
    left:50%;
    transform: translate(-300px);
    backface-visibility:hidden;
    animation: in 500ms 6s ease-out forwards;
}
.cake:after,
.cake:before{
    content: "";
    position: absolute;
    background-color: rgba(255,0,0,0.4);
    width: 100px;
    height:2.2222222px;
    border-radius: 10px;
}